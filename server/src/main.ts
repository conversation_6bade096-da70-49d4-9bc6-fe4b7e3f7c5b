import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // 启用 CORS
  app.enableCors({
    origin: ['http://localhost:3000', 'http://127.0.0.1:3000'], // 允许的前端域名
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'], // 允许的HTTP方法
    allowedHeaders: ['Content-Type', 'Authorization', 'Accept'], // 允许的请求头
    credentials: true, // 允许携带凭证
  });

  await app.listen(3001);
  console.log('Deepsearch server running on http://localhost:3001');
}
bootstrap();
